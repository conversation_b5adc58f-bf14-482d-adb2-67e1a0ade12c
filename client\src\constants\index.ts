/**
 * Application constants and configuration
 */

import type { ProjectData, NavigationItem, ParticleConfig, ThemeColors } from '@/types';

// Animation constants
export const ANIMATION_DURATIONS = {
  FAST: 0.2,
  NORMAL: 0.5,
  SLOW: 1.0,
  VERY_SLOW: 2.0,
} as const;

export const ANIMATION_DELAYS = {
  NONE: 0,
  SHORT: 0.1,
  MEDIUM: 0.3,
  LONG: 0.5,
} as const;

export const ANIMATION_EASINGS = {
  POWER2_OUT: 'power2.out',
  POWER2_IN: 'power2.in',
  POWER2_IN_OUT: 'power2.inOut',
  BACK_OUT: 'back.out(1.7)',
  BOUNCE_OUT: 'bounce.out',
  ELASTIC_OUT: 'elastic.out(1, 0.3)',
} as const;

// Letter animation types
export const LETTER_ANIMATION_TYPES = [
  'glow',
  'rotation', 
  'bounce',
  'flip',
  'pulse'
] as const;

// Color palette
export const COLORS: ThemeColors = {
  primary: '#FF3366',
  secondary: '#4A90E2', 
  accent: '#F5F5F5',
  background: '#1A1A1A',
  foreground: '#F5F5F5',
} as const;

export const COLOR_VARIANTS = {
  PINK: '#FF3366',
  BLUE: '#4A90E2',
  WHITE: '#F5F5F5',
  DARK: '#1A1A1A',
  GRAY: '#A1A1AA',
} as const;

// Navigation items
export const NAVIGATION_ITEMS: NavigationItem[] = [
  {
    label: 'Portfolio',
    href: '#portfolio',
    color: COLOR_VARIANTS.PINK,
  },
  {
    label: 'About', 
    href: '#about',
    color: COLOR_VARIANTS.BLUE,
  },
  {
    label: 'Contact',
    href: '#contact', 
    color: COLOR_VARIANTS.PINK,
  },
] as const;

// Rotating titles
export const ROTATING_TITLES = [
  'Creative Developer & Designer',
  'AI-First App Builder',
  'Full-Stack Developer', 
  'Web Innovator & Experience Crafter',
] as const;

// Project data
export const PROJECTS: ProjectData[] = [
  {
    id: 1,
    title: 'NeuralFlow AI Platform',
    description: 'A fully animated portfolio site built with React and GSAP. My first full-stack project after Angela Yu’s course.',
    type: 'Creative Dev Build',
    placeholder: 'https://via.placeholder.com/400x250/1A1A1A/FF3366?text=AI+Platform',
    technologies: ['Python', 'TensorFlow', 'React', 'FastAPI'],
    status: 'coming-soon',
    hasVideo: true,
  },
  {
    id: 2,
    title: 'VLM Mobile App',
    description: 'App using vision language models to help make life easier. Currently in development as a side project.',
    type: 'VLM Experimentation',
    placeholder: 'https://via.placeholder.com/400x250/1A1A1A/4A90E2?text=Design+Tool',
    technologies: ['React', 'GSAP', 'Canvas API', 'WebGL'],
    status: 'coming-soon',
  },
] as const;

// Particle configuration
export const PARTICLE_CONFIG: ParticleConfig = {
  count: 50,
  speed: 1,
  opacity: 0.6,
  size: 2,
} as const;

// UFO animation configuration
export const UFO_CONFIG = {
  TRIGGER_DELAY: 8000, // 8 seconds
  FLIGHT_DURATION: 3000, // 3 seconds
  ABDUCTION_DURATION: 2000, // 2 seconds
  RETURN_DELAY: 3000, // 3 seconds
  ALIEN_GLYPHS: ['◊', '◈', '◇', '◉', '⬢', '⬡', '⟐', '⟡'],
} as const;

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  MAX_PARTICLES: 100,
  MIN_FRAME_RATE: 30,
  MAX_MEMORY_USAGE: 50, // MB
  ANIMATION_TIMEOUT: 10000, // 10 seconds
} as const;

// Accessibility settings
export const ACCESSIBILITY_SETTINGS = {
  REDUCED_MOTION_QUERY: '(prefers-reduced-motion: reduce)',
  HIGH_CONTRAST_QUERY: '(prefers-contrast: high)',
  FOCUS_VISIBLE_CLASS: 'focus-visible',
} as const;

// Breakpoints (matching Tailwind)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;

// Mouse effects configuration
export const MOUSE_EFFECTS_CONFIG = {
  PARALLAX: {
    STAR_LAYERS: 3,
    MAX_MOVEMENT: 50, // pixels
    SMOOTHING: 0.1,
    THROTTLE_MS: 16, // ~60fps
  },
  CURSOR_TRAIL: {
    PARTICLE_COUNT: 16,
    PARTICLE_LIFE: 800,
    SPAWN_RATE: 30,
    SIZE_RANGE: [3, 8],
    OPACITY_RANGE: [0.5, 0.9],
  },
  STARS: {
    LAYER_1_COUNT: 30, // closest layer
    LAYER_2_COUNT: 20, // middle layer
    LAYER_3_COUNT: 15, // farthest layer
    SIZE_RANGE: [1, 3],
    OPACITY_RANGE: [0.3, 0.9],
    COLORS: ['#ffffff', '#e0f2fe', '#dbeafe', '#f0f9ff'],
  },
} as const;

// Contact section configuration
export const CONTACT_CONFIG = {
  SPACESHIP_FLIGHT_DURATION: 2.0,
  LASER_TRACE_DURATION: 1.5,
  FORM_ANIMATION_STAGGER: 0.2,
  EMAIL_ADDRESS: '<EMAIL>', // Replace with actual email
  SPACESHIP_HOVER_SCALE: 1.1,
  SPACESHIP_HOVER_GLOW_INTENSITY: 1.5,
  SPACESHIP_FLOAT_AMPLITUDE: 10,
  // Sequential animation timing - ensures cryptic text completes before heading reveal
  CRYPTIC_SEQUENCE_TOTAL_DURATION: 8.3, // Total time for cryptic text cycle + explosion + restoration
} as const;

// Glitch effect configuration for hero section letters
export const GLITCH_CONFIG = {
  TARGET_LETTERS: ['A', 'I'], // Letters to apply glitch effect to in "JAIME"
  LOOP_INTERVAL_MIN: 3000, // Minimum time between glitch cycles (3 seconds)
  LOOP_INTERVAL_MAX: 5000, // Maximum time between glitch cycles (5 seconds)
  GLITCH_DURATION_MIN: 100, // Minimum duration of single glitch (0.1 seconds)
  GLITCH_DURATION_MAX: 300, // Maximum duration of single glitch (0.3 seconds)
  OPACITY_RANGE: [0.3, 1.0], // Opacity flicker range
  SKEW_RANGE: [-5, 5], // Skew transform range in degrees
  TEXT_SHADOW_COLORS: ['#FF3366', '#4A90E2', '#00FFFF'], // Glitch shadow colors
  COLOR_VARIANTS: ['#FF3366', '#4A90E2', '#FFFFFF'], // Brief color changes
} as const;

// Z-index layers
export const Z_INDEX = {
  BACKGROUND: 0,
  PARALLAX_STARS: 1,
  PARTICLES: 2,
  CURSOR_TRAIL: 3,
  CONTENT: 10,
  NAVIGATION: 20,
  UFO: 50,
  MODAL: 100,
  TOOLTIP: 1000,
} as const;
